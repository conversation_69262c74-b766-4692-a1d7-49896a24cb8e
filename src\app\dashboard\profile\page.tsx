"use client";

import { useUser } from "@/components/AppProvider";
import MyEmail from "@/components/Profile/MyEmail";
import PersonalInformation from "@/components/Profile/PersonalInformation";
import SubscriptionAndBilling from "@/components/Profile/SubscriptionAndBilling";
import { Card } from "@/components/ui/card";
import React from "react";

const Profile = () => {
  const userData = useUser();

  if (!userData) {
    return null;
  }

  return (
    <div className="flex-1 h-full Support">
      <Card className="h-full glassColor relative p-6">
        <PersonalInformation userData={userData} />
        <MyEmail userData={userData} />
        <SubscriptionAndBilling />
      </Card>
    </div>
  );
};

export default Profile;
