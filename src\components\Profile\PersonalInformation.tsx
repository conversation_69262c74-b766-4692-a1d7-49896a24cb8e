import React, { useEffect, useState } from "react";
import { Card } from "../ui/card";
import SectionHeader, { SectionHeaderName } from "./SectionHeader";
import { Button } from "../ui/button";
import { Edit2 } from "lucide-react";
import Image from "next/image";
import { Input } from "../ui/input";
import { Country, CountryDropdown } from "../ui/country-dropdown";
import { PhoneInput } from "../ui/phone-input";
import { countries } from "country-data-list";
import { UsersType } from "@/types/db";
import { toast } from "sonner";
import { CountryCode, isValidPhoneNumber } from "libphonenumber-js";
import { useUpdateUser } from "../AppProvider";

type PersonalInformationProps = React.FC<{
  userData: UsersType;
}>;
const PersonalInformation: PersonalInformationProps = ({ userData }) => {
  console.log("userData", userData);
  const updateUser = useUpdateUser();
  const [isEdit, setIsEdit] = useState(false);
  const [img, setImg] = useState<File | string | null>(null);
  const [name, setName] = useState("");
  const [country, setCountry] = useState<Country>(countries.all[0]);
  const [phoneNumber, setPhoneNumber] = useState<{
    phone: string;
    country: Country;
  }>({
    country: countries.all[0],
    phone: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const startStates = () => {
    setImg(userData?.avatarUrl);
    setName(String(userData?.name));
    const countryDetails = countries.all.find(
      (f) => f.name === userData?.country
    );
    if (countryDetails) {
      setCountry(countryDetails);
    }
    const phoneCountry = countries.all.find(
      (f) => f.name === userData?.phoneCountry
    );
    setPhoneNumber({
      country: phoneCountry ?? countries.all[0],
      phone: String(userData?.phone),
    });
  };

  // Function to check if any values have changed
  const hasChanges = () => {
    if (!userData) return false;

    const originalCountry = countries.all.find(
      (f) => f.name === userData.country
    );
    const originalPhoneCountry = countries.all.find(
      (f) => f.name === userData.phoneCountry
    );

    return (
      img !== userData.avatarUrl ||
      name !== String(userData.name) ||
      country.name !== (originalCountry?.name || countries.all[0].name) ||
      phoneNumber.phone !== String(userData.phone) ||
      phoneNumber.country.name !==
        (originalPhoneCountry?.name || countries.all[0].name)
    );
  };

  useEffect(() => {
    if (userData) {
      startStates();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userData, countries]);

  console.log("userData", userData);

  const imgSrc = (() => {
    if (typeof img === "string") {
      return img;
    }
    if (img?.name) {
      return URL.createObjectURL(img);
    }
    return "";
  })();

  const handleSelectImage = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setImg(file);
      }
    };
    input.click();
  };

  const buttonClassName =
    "w-[100px] text-black bg-white hover:bg-white/90 cursor-pointer";

  const commonInputClass =
    "bg-white placeholder:text-[#989898] shadow-none focus:outline-none focus:ring-0 focus:shadow-none";

  const uploadImageToSupabase = async (file: File): Promise<string | null> => {
    try {
      const fileExtension = file.name.split(".").pop();
      const fileName = `${userData.id}.${fileExtension}`;
      const filePath = `avatars/${fileName}`;

      const response = await fetch("/api/profile/upload-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fileName: filePath,
          fileType: file.type,
        }),
      });

      if (!response.ok) throw new Error("Failed to get upload URL");

      const { uploadUrl, filePath: returnedPath } = await response.json();

      const uploadResponse = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) throw new Error("Failed to upload image");

      return returnedPath;
    } catch (error) {
      console.error("Upload error:", error);
      setIsSubmitting(false);
      return null;
    }
  };

  const handleSaveChanges = async () => {
    if (!img) {
      toast.error("Profile image is required");
      return;
    }
    if (name.length < 1) {
      toast.error("Name is too small");
      return;
    }
    if (!country) {
      toast.error("Country is required");
      return;
    }
    if (
      !isValidPhoneNumber(
        phoneNumber.phone,
        phoneNumber.country.alpha2 as CountryCode
      )
    ) {
      toast.error("Phone number is not valid");
      return;
    }

    const handleError = () => {
      toast.error("Something went wrong while updating profile");
    };

    try {
      setIsSubmitting(true);
      let avatarId = null;
      if (img instanceof File) {
        avatarId = img ? await uploadImageToSupabase(img) : null;
      }

      const response = await fetch("/api/profile/update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          country: country.name,
          phone: phoneNumber.phone,
          phoneCountry: phoneNumber.country.name,
          avatarId,
        }),
      });

      if (response.ok) {
        setIsEdit(false);
        const responseData = await response.json();
        if (responseData.avatarUrl) {
          setImg(responseData.avatarUrl);
        }
        updateUser({
          name,
          country: country.name,
          phone: phoneNumber.phone,
          phoneCountry: phoneNumber.country.name,
          avatarUrl:
            responseData?.avatarUrl.length > 0
              ? responseData?.avatarUrl
              : userData.avatarUrl,
        });
        toast.success("Profile updated sucessfully");
      } else {
        handleError();
        throw new Error("Failed to update profile");
      }
      setIsSubmitting(false);
    } catch (error) {
      setIsSubmitting(false);
      handleError();
      console.error("Update error:", error);
    }
  };

  const handleCancel = () => {
    startStates();
    setIsEdit(false);
  };

  return (
    <Card
      className="h-full glassColor relative p-4"
      aria-disabled={isSubmitting}
    >
      <SectionHeader type={SectionHeaderName.PersonalInfo}>
        <>
          {!isEdit && (
            <Button
              onClick={() => {
                setIsEdit((prev) => !prev);
              }}
              className="border cursor-pointer border-[#16A34A] bg-transparent text-black hover:bg-transparent p-1"
            >
              <Edit2 />
              Edit
            </Button>
          )}
        </>
      </SectionHeader>
      <div className="h-full flex flex-col justify-between">
        <div>
          <div className="flex flex-col gap-4 mb-6 w-full md:flex-row">
            <div className="h-[125px] w-[125px] rounded-full bg-white relative overflow-hidden">
              {imgSrc && <Image fill src={imgSrc} alt="profile image" />}
            </div>
            {isEdit && (
              <div className="flex flex-col gap-4 items-center justify-center">
                <Button
                  disabled={isSubmitting}
                  onClick={() => {
                    handleSelectImage();
                  }}
                  className={buttonClassName}
                >
                  Edit
                </Button>
                <Button
                  disabled={isSubmitting}
                  onClick={() => {
                    setImg(null);
                  }}
                  className={buttonClassName}
                >
                  Remove
                </Button>
              </div>
            )}
          </div>
          <div className="w-full flex flex-wrap items-center justify-between">
            {isEdit ? (
              <>
                <Wrapper label="Name">
                  <Input
                    className={commonInputClass}
                    type="text"
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value);
                    }}
                    placeholder="Enter your name"
                  />
                </Wrapper>
                <Wrapper label="Phone">
                  <div className="flex items-center w-full">
                    <CountryDropdown
                      className={commonInputClass}
                      defaultValue={phoneNumber?.country?.alpha3}
                      placeholder="Select your country"
                      onChange={(country) => {
                        console.log("country", country);
                        setPhoneNumber((prev) => ({
                          ...prev,
                          country: country,
                        }));
                      }}
                      inline
                    />
                    <PhoneInput
                      value={phoneNumber.phone}
                      className={commonInputClass}
                      placeholder="Enter your phone number"
                      onChange={(e) => {
                        const value = e.target.value;
                        setPhoneNumber((prev) => ({
                          ...prev,
                          phone: value,
                        }));
                      }}
                      inline
                    />
                  </div>
                </Wrapper>
                <Wrapper label="Country">
                  <CountryDropdown
                    className={commonInputClass}
                    defaultValue={country?.alpha3}
                    placeholder="Select your country"
                    onChange={(country) => {
                      console.log("country", country);
                      setCountry(country);
                    }}
                  />
                </Wrapper>
              </>
            ) : (
              <>
                <InputValues label="Name" value={name} />
                <InputValues label="Phone" value={phoneNumber.phone} />
                <InputValues label="Country" value={country?.name} />
                <InputValues label="Email ID" value={String(userData.email)} />
              </>
            )}
          </div>
        </div>

        {isEdit && (
          <div className="w-full flex gap-2 items-center justify-center">
            <Button
              disabled={isSubmitting}
              onClick={() => {
                handleCancel();
              }}
              variant="outline"
              className="w-1/3 cursor-pointer border-2 bg-transparent hover:bg-transparent border-[#16A34A] text-[#16A34A] hover:text-[#16A34A]"
            >
              Cancel
            </Button>
            <Button
              disabled={isSubmitting || !hasChanges()}
              className="w-1/3 cursor-pointer primaryButton"
              onClick={() => {
                handleSaveChanges();
              }}
            >
              Save Changes
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

type WrapperProps = React.FC<{
  label: string;
  children: React.ReactNode;
}>;
const Wrapper: WrapperProps = ({ label, children }) => {
  return (
    <div className="w-full md:w-1/2 p-2">
      <label className="text-[0.7rem] font-semibold text-[#606060] mb-1">
        {label}
      </label>
      {children}
    </div>
  );
};

type InputValuesProps = React.FC<{
  label: string;
  value: string;
}>;
const InputValues: InputValuesProps = ({ label, value }) => {
  return (
    <div className="w-full md:w-1/2 mb-4 p-2">
      <label className="text-[0.7rem] font-semibold text-[#606060] mb-1">
        {label}
      </label>
      <p className="text-[0.8rem] font-bold mb-1">
        {value.length > 0 ? value : "-"}
      </p>
    </div>
  );
};

export default PersonalInformation;
