import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, country, phone, avatarId, phoneCountry } =
      await request.json();

    const payload = {
      id: user.id,
      name,
      country,
      phone,
      phoneCountry,
      avatarId,
      updatedAt: new Date().toISOString(),
    };

    if (!payload.avatarId) {
      delete payload.avatarId;
    }

    const { error } = await supabase.from("users").upsert(payload);

    let avatarUrl = "";
    if (avatarId) {
      const { data: avatarData } = await supabase.storage
        .from("avatar")
        .createSignedUrl(avatarId, 60 * 60);
      avatarUrl = avatarData?.signedUrl ?? "";
    }

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      avatarUrl,
    });
  } catch (error) {
    console.log("Something went wrong in api/profile/update due to ", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
