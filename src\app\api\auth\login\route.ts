import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const supabase = await createClient();

    const data = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
    };

    // Validate required fields
    if (!data.email || !data.password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    const { error } = await supabase.auth.signInWithPassword(data);
    console.log("Login attempt error:", error);

    if (error) {
      if (error.message.includes("Invalid login credentials")) {
        // Check if user exists in our users table and what provider they used
        const { data: existingUser, error: getUserError } = await supabase
          .from("users")
          .select("id, email, provider")
          .eq("email", data.email)
          .maybeSingle();

        if (!getUserError && existingUser) {
          // User exists in our database, but login failed
          // Check what provider they used to sign up
          if (existingUser.provider === "google") {
            return NextResponse.json(
              {
                error:
                  "This email is associated with a Google account. Please sign in with Google instead.",
                isOAuthUser: true,
              },
              { status: 401 }
            );
          } else if (existingUser.provider === "email") {
            // User signed up with email/password but credentials are wrong
            return NextResponse.json(
              {
                error:
                  "Invalid email or password. Please check your credentials and try again.",
              },
              { status: 401 }
            );
          }
        }

        if (!getUserError && !existingUser) {
          return NextResponse.json(
            {
              error:
                "This email is not associated with an account. Please sign up first.",
            },
            { status: 401 }
          );
        }
      }

      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    return NextResponse.json(
      {
        success: true,
        message: "Login successful",
        redirectTo: "/dashboard",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
