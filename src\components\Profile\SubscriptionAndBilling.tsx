"use client";

import React, { useState } from "react";
import { Card } from "../ui/card";
import SectionHeader, { SectionHeaderName } from "./SectionHeader";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

const sizes = {
  height: 20,
  width: 20,
};

const SubscriptionAndBilling = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const getBillingUrl = async () => {
    const handleError = () => {
      setIsLoading(false);
      toast("Something went wrong.Please try again");
    };

    try {
      setIsLoading(true);
      const response = await fetch("/api/stripe/customer-portal", {
        method: "GET",
      });
      if (!response.ok) {
        handleError();
        return;
      } else {
        const responseData = await response.json();
        if (responseData.data) {
          router.push(responseData.data);
          toast.success("Redirecting you to the stripe customer portal");
        } else {
          handleError();
        }
      }
      setIsLoading(false);
    } catch (error) {
      handleError();
      console.error("Something went wrong in getBillingUrl due to ", error);
    }
  };

  return (
    <Card className="glassColor relative p-4 gap-0">
      <SectionHeader type={SectionHeaderName.Subscription} />
      <div className="mt-2 gap-1 flex flex-col">
        <ListItem
          onClick={() => {
            getBillingUrl();
          }}
          isLoading={isLoading}
          name="Manage Subscription"
        />
        <ListItem
          onClick={() => {
            getBillingUrl();
          }}
          isLoading={isLoading}
          name="Billing History"
        />
      </div>
    </Card>
  );
};

export default SubscriptionAndBilling;

type ListItemProps = React.FC<{
  name: string;
  isLoading: boolean;
  onClick: () => void;
}>;
const ListItem: ListItemProps = ({ name, isLoading, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="flex cursor-pointer flex-row items-center justify-between text-[#005857]"
    >
      <p className="m-0 text-[#005857] font-semibold text-[0.85rem]">{name}</p>
      {isLoading ? (
        <Spinner size={20} color="text-[#005857]" />
      ) : (
        <svg
          {...sizes}
          viewBox="0 0 24 24"
          data-name="Flat Color"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="#005857"
            d="m21.71 11.29-7-7a1 1 0 0 0-1.42 1.42l5.3 5.29H3a1 1 0 0 0 0 2h15.59l-5.3 5.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l7-7a1 1 0 0 0 0-1.42"
          />
        </svg>
      )}
    </div>
  );
};

const Spinner = ({ size = 40, color = "text-primary" }) => {
  return (
    <svg
      className={`animate-spin ${color}`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      width={size}
      height={size}
    >
      <circle
        className="opacity-20"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-80"
        fill="currentColor"
        d="M4 12a8 8 0 018-8v2a6 6 0 00-6 6H4z"
      />
    </svg>
  );
};
