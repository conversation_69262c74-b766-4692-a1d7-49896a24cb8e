import React from "react";
import { Card } from "../ui/card";
import Section<PERSON>ead<PERSON>, { SectionHeaderName } from "./SectionHeader";
import { UsersType } from "@/types/db";
import { timeAgo } from "@/lib/date";

type MyEmailProps = React.FC<{
  userData: UsersType;
}>;
const MyEmail: MyEmailProps = ({ userData }) => {
  return (
    <Card className="glassColor relative p-4 gap-1">
      <SectionHeader type={SectionHeaderName.MyEmail} />
      <div>
        <p className="font-bold text-[0.9rem] mb-0">{userData.email}</p>
        {userData?.createdAt && (
          <p className="text-[#545454] text-[0.85rem]">
            joined {timeAgo(userData?.createdAt)}
          </p>
        )}
      </div>
    </Card>
  );
};

export default MyEmail;
