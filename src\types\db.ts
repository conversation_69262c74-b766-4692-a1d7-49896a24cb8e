import { Maybe } from "./common";

export type UsersType = {
  id: string;
  plan_id: string | null;
  plan: Maybe<PlansType>;
  credits_balance: number | null;
  credits_used_month: number | null;
  stripecustomerid: string | null;
  last_reset: string | null; // ISO date
  email: string | null;
  avatarId: string | null;
  name: string | null;
  phone: string | null;
  country: string | null;
  phoneCountry: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  provider: string | null;

  avatarUrl: string | null;
};

export type PlansType = {
  id: string;
  name: string | null;
  price_cents: number | null;
  daily_cap: number | null;
  monthly_cap: number | null;
  keyword_limit: number | null;
  prospected_filter: string | null;
  networks_limit: string | null;
  followers_filter: string | null;
};

export type CreditsLogType = {
  id: string;
  user_id: Maybe<UsersType | string>;
  delta: number | null;
  reason: string | null;
  ref: string | null;
  created_at: string | null; // ISO timestamp
};

export type LeadsType = {
  id: string;
  domain: string | null;
  email: string | null;
  phone: string | null;
  social: Record<string, unknown> | null;
  score: number | null;
  credit_cost: number | null;
  lives_left: number | null;
  prospected_count: number | null;
  listed_tier: string | null;
  updated_at: string | null; // ISO timestamp
};

export type OrdersType = {
  id: string;
  user_id: string | null;
  total_credits: number | null;
  total_leads: number | null;
  amount_cents: number | null;
  created_at: string | null; // ISO timestamp
};

export type SupportType = {
  id: number;
  created_at: string;
  email: string | null;
  subject: string | null;
  message: string | null;
  name: string | null;
  userId: string | null;
};
