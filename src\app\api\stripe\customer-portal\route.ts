import { createClient } from "@/lib/server";
import { getStripeCustomerPortalLink } from "@/lib/stripe";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 }
      );
    }

    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("stripecustomerid")
      .eq("id", user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json(
        { error: "Failed to retrieve user data" },
        { status: 500 }
      );
    }

    const { url } = await getStripeCustomerPortalLink(
      userData.stripecustomerid
    );

    if (url) {
      return NextResponse.json(
        {
          success: true,
          data: url,
          message: "Generated portal link successfully",
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to generate customer portal link",
      },
      { status: 400 }
    );
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred", success: false },
      { status: 500 }
    );
  }
}
