import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { fileName } = await request.json();

    const fileExtension = fileName.split(".").pop();
    const userFileName = `${user.id}.${fileExtension}`;

    const { data, error } = await supabase.storage
      .from("avatar")
      .createSignedUploadUrl(userFileName, {
        upsert: true,
      });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      uploadUrl: data.signedUrl,
      filePath: userFileName,
    });
  } catch (error) {
    console.error("Something went wrong due to ", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
