/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require("fs");
const path = require("path");
const { stringify } = require("csv-stringify");
const { parse } = require("csv-parse");

const parseFollowerCount = (val) => {
  if (!val) return 0;
  const str = val.toString().trim();
  if (/^\d+(\.\d+)?[Mm]$/.test(str)) {
    return Math.round(parseFloat(str) * 1_000_000);
  }
  if (/^\d+(\.\d+)?[Kk]$/.test(str)) {
    return Math.round(parseFloat(str) * 1_000);
  }
  const num = parseInt(str.replace(/,/g, ""), 10);
  return isNaN(num) ? 0 : num;
};

try {
  const inputFile = path.join(__dirname, "input.csv");
  const outputFile = path.join(__dirname, "output.csv");
  const rows = [];

  fs.createReadStream(inputFile)
    .pipe(parse({ columns: true }))
    .on("data", (row) => {
      const social = {};

      // Map URLs and follower counts for each platform
      if (row.facebook_domain) {
        social.fb = {
          url: row.facebook_domain,
          followers_count: 0, // No follower count column for Facebook
        };
      }

      if (row.instagram_domain) {
        social.ig = {
          url: row.instagram_domain,
          followers_count: parseFollowerCount(row.INSTAGRAM),
        };
      }

      if (row.youtube_domain) {
        social.youtube = {
          url: row.youtube_domain,
          followers_count: parseFollowerCount(row.YOUTUBE),
        };
      }

      if (row.tiktok_domain) {
        social.tiktok = {
          url: row.tiktok_domain,
          followers_count: parseFollowerCount(row.TIKTOK),
        };
      }

      if (row.twitter_domain) {
        social.x = {
          url: row.twitter_domain,
          followers_count: 0, // No follower count column for Twitter/X
        };
      }

      if (row.linkedin_domain) {
        social.linkedin = {
          url: row.linkedin_domain,
          followers_count: 0, // No follower count column for LinkedIn
        };
      }

      rows.push({
        domain: row.domain,
        email: row.email,
        phone: row.phone_numbers,
        categories: row.categorie ?? "",
        social: JSON.stringify(social),
        source: row.source,
      });
    })
    .on("end", () => {
      const addHeader = !fs.existsSync(outputFile);
      stringify(
        rows,
        {
          header: addHeader,
          columns: [
            "domain",
            "email",
            "phone",
            "categories",
            "social",
            "source",
          ],
        },
        (err, output) => {
          if (err) throw err;
          fs.appendFileSync(outputFile, output);
          console.log(
            "CSV file successfully processed and saved to",
            outputFile
          );
        }
      );
    })
    .on("error", (err) => {
      console.error("Error processing CSV:", err);
    });
} catch (error) {
  console.error("Something went wrong:", error);
}
