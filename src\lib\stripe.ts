/* eslint-disable @typescript-eslint/no-explicit-any */
import <PERSON><PERSON> from "stripe";
import { getNameFromEmail } from "./utils";

const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY;
const stripe = new Stripe(STRIPE_SECRET_KEY!);

type createStripeCustomerProps = {
  email: string;
  userId: string;
};

export async function createStripeCustomer({
  email,
  userId,
}: createStripeCustomerProps): Promise<Stripe.Customer> {
  try {
    const name = getNameFromEmail(email);
    const customer = await stripe.customers.create({
      email,
      name: name ?? "",
      metadata: { userId },
    });

    console.log("Stripe customer created:", {
      id: customer.id,
      email: customer.email,
    });

    return customer;
  } catch (error) {
    console.error("Failed to create Stripe customer:", error);
    throw new Error("Failed to create Stripe customer");
  }
}

export const getStripeCustomerPortalLink = async (customerId: string) => {
  try {
    const portal = await stripe.billingPortal.sessions.create({
      customer: customerId,
    });
    console.log("portal details", portal);
    return {
      url: portal?.url,
      noCustomer: false,
    };
  } catch (error: any) {
    const noCustomer =
      error?.raw?.message?.includes("No such customer:") &&
      error?.raw?.statusCode === 400;
    console.error(
      "Something went wrong in getStripeCustomerPortalLink due to",
      error
    );
    return {
      url: "",
      noCustomer,
    };
  }
};

export async function createCustomerId(
  email: string,
  userId: string
): Promise<string | null> {
  try {
    const stripeCustomer = await createStripeCustomer({
      email,
      userId,
    });
    console.log("Stripe customer created:", stripeCustomer.id);
    return stripeCustomer.id;
  } catch (error) {
    console.error("Failed to create Stripe customer:", error);
    // Return null to allow the registration process to continue
    // The customer can be created later if needed
    return null;
  }
}

export { stripe };
