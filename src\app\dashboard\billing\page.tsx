"use client";

import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";

const Profile = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const getBillingUrl = async () => {
    const handleError = () => {
      setIsLoading(false);
      toast("Something went wrong.Please try again");
    };

    try {
      setIsLoading(true);
      const response = await fetch("/api/stripe/customer-portal", {
        method: "GET",
      });
      if (!response.ok) {
        handleError();
        return;
      } else {
        const responseData = await response.json();
        if (responseData.data) {
          router.push(responseData.data);
          toast.success("Redirecting you to the stripe customer portal");
        } else {
          handleError();
        }
      }
      setIsLoading(false);
    } catch (error) {
      handleError();
      console.error("Something went wrong in getBillingUrl due to ", error);
    }
  };

  return (
    <div>
      <Button
        className="primaryButton"
        disabled={isLoading}
        onClick={getBillingUrl}
      >
        View Billing
      </Button>
    </div>
  );
};

export default Profile;
