import React from "react";

export enum SectionHeaderName {
  PersonalInfo = "PersonalInfo",
  MyEmail = "MyEmail",
  Subscription = "Subscription",
}

type SectionHeaderProps = React.FC<{
  type: SectionHeaderName;
  children?: React.ReactNode;
}>;

const SectionHeader: SectionHeaderProps = ({ type, children }) => {
  return (
    <div className="flex flex-row w-full items-center justify-between">
      <div className="flex flex-row gap-2">
        <span>
          {type === SectionHeaderName.PersonalInfo && <UserIcon />}
          {type === SectionHeaderName.MyEmail && <MailIcon />}
          {type === SectionHeaderName.Subscription && <SubscriptionIcon />}
        </span>
        <span className="font-bold">{getSectionName(type)}</span>
      </div>
      {children && <>{children}</>}
    </div>
  );
};

export default SectionHeader;

const getSectionName = (name: SectionHeaderName) => {
  switch (name) {
    case SectionHeaderName.PersonalInfo:
      return "Personal Information";
    case SectionHeaderName.MyEmail:
      return "My Email Address";
    case SectionHeaderName.Subscription:
      return "Subscription & Billing ";
    default:
      return "";
  }
};

const sizes = {
  width: 24,
  height: 24,
};

const UserIcon = () => {
  return (
    <svg
      {...sizes}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 5C16.3261 5 17.5979 5.52678 18.5355 6.46447C19.4732 7.40215 20 8.67392 20 10C20 11.3261 19.4732 12.5979 18.5355 13.5355C17.5979 14.4732 16.3261 15 15 15C13.6739 15 12.4021 14.4732 11.4645 13.5355C10.5268 12.5979 10 11.3261 10 10C10 8.67392 10.5268 7.40215 11.4645 6.46447C12.4021 5.52678 13.6739 5 15 5ZM15 7.5C14.337 7.5 13.7011 7.76339 13.2322 8.23223C12.7634 8.70107 12.5 9.33696 12.5 10C12.5 10.663 12.7634 11.2989 13.2322 11.7678C13.7011 12.2366 14.337 12.5 15 12.5C15.663 12.5 16.2989 12.2366 16.7678 11.7678C17.2366 11.2989 17.5 10.663 17.5 10C17.5 9.33696 17.2366 8.70107 16.7678 8.23223C16.2989 7.76339 15.663 7.5 15 7.5ZM15 16.25C18.3375 16.25 25 17.9125 25 21.25V25H5V21.25C5 17.9125 11.6625 16.25 15 16.25ZM15 18.625C11.2875 18.625 7.375 20.45 7.375 21.25V22.625H22.625V21.25C22.625 20.45 18.7125 18.625 15 18.625Z"
        fill="black"
      />
    </svg>
  );
};

const MailIcon = () => {
  return (
    <svg
      {...sizes}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 20C3.45 20 2.97933 19.8043 2.588 19.413C2.19667 19.0217 2.00067 18.5507 2 18V6C2 5.45 2.196 4.97933 2.588 4.588C2.98 4.19667 3.45067 4.00067 4 4H20C20.55 4 21.021 4.196 21.413 4.588C21.805 4.98 22.0007 5.45067 22 6V18C22 18.55 21.8043 19.021 21.413 19.413C21.0217 19.805 20.5507 20.0007 20 20H4ZM20 8L12.525 12.675C12.4417 12.725 12.3543 12.7627 12.263 12.788C12.1717 12.8133 12.084 12.8257 12 12.825C11.916 12.8243 11.8287 12.812 11.738 12.788C11.6473 12.764 11.5597 12.7263 11.475 12.675L4 8V18H20V8ZM12 11L20 6H4L12 11ZM4 8.25V6.775V6.8V6.788V8.25Z"
        fill="black"
      />
    </svg>
  );
};

const SubscriptionIcon = () => {
  return (
    <svg
      {...sizes}
      viewBox="0 0 23 27"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.2031 3.375C21.4502 3.375 21.6823 3.42773 21.8994 3.5332C22.1165 3.63867 22.3075 3.79248 22.4722 3.99463C22.6369 4.19678 22.7642 4.4209 22.854 4.66699C22.9438 4.91309 22.9925 5.18555 23 5.48438V19.8281C23 20.1182 22.9551 20.3906 22.8652 20.6455C22.7754 20.9004 22.6444 21.1245 22.4722 21.3179C22.3 21.5112 22.109 21.6606 21.8994 21.7661C21.6898 21.8716 21.4577 21.9287 21.2031 21.9375H1.79688C1.5498 21.9375 1.31771 21.8848 1.10059 21.7793C0.883464 21.6738 0.692546 21.52 0.527832 21.3179C0.363118 21.1157 0.23584 20.8916 0.145996 20.6455C0.0561523 20.3994 0.00748698 20.127 0 19.8281V5.48438C0 5.19434 0.0449219 4.92188 0.134766 4.66699C0.224609 4.41211 0.355632 4.18799 0.527832 3.99463C0.700033 3.80127 0.890951 3.65186 1.10059 3.54639C1.31022 3.44092 1.54232 3.38379 1.79688 3.375H21.2031ZM1.79688 5.0625C1.69206 5.0625 1.60596 5.10205 1.53857 5.18115C1.47119 5.26025 1.4375 5.36133 1.4375 5.48438V8.4375H21.5625V5.48438C21.5625 5.36133 21.5288 5.26025 21.4614 5.18115C21.394 5.10205 21.3079 5.0625 21.2031 5.0625H1.79688ZM21.2031 20.25C21.3079 20.25 21.394 20.2104 21.4614 20.1313C21.5288 20.0522 21.5625 19.9512 21.5625 19.8281V10.125H1.4375V19.8281C1.4375 19.9512 1.47119 20.0522 1.53857 20.1313C1.60596 20.2104 1.69206 20.25 1.79688 20.25H21.2031ZM15.8125 15.1875H18.6875V16.875H15.8125V15.1875Z"
        fill="black"
      />
    </svg>
  );
};
