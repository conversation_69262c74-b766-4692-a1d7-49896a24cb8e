"use client";

import React, { useState } from "react";
import { Card } from "../ui/card";
import { Button } from "../ui/button";
import SidebarToggle from "./SidebarToggle";
import SidebarModal from "./SidebarModal";
import Logo from "../Logo";
import { useUser } from "../AppProvider";
import UserDropdown from "../UserDropdown";

const DashboardHeader = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const userData = useUser();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <>
      <div className="w-full flex items-center justify-between">
        <div className="flex flex-row items-center gap-2">
          <SidebarToggle onClick={toggleSidebar} />
          <Logo />
        </div>

        <div className="flex items-center gap-3">
          <PlanInfo />
          <UserDropdown userData={userData!} />
        </div>
      </div>

      <SidebarModal isOpen={isSidebarOpen} onClose={closeSidebar} />
    </>
  );
};

const PlanInfo = () => {
  const userData = useUser();
  const planName = userData?.plan?.name || "Free";
  const isFreePlan = planName.toLowerCase() === "free";

  return (
    <Card className="flex flex-row p-1 gap-3 rounded-full glassColor text-center items-center">
      <span className="text-sm text-[rgba(57, 57, 57, 1)]">
        Plan: {planName}
      </span>
      {isFreePlan && (
        <Button
          size="sm"
          className="primaryButton rounded-full px-4 py-2 h-auto text-xs"
        >
          Upgrade Plan
        </Button>
      )}
    </Card>
  );
};

export default DashboardHeader;
