import { DashboardIconType } from "@/constant/dashboard";
import React from "react";

const sizes = {
  width: "20",
  height: "20",
};

type DashboardIconProps = React.FC<{
  type: DashboardIconType;
}>;
const DashboardIcon: DashboardIconProps = ({ type }) => {
  const fillColor = "#005857";
  if (type === DashboardIconType.SUPPORT) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 28 28"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M14.0002 20.3004C13.2963 20.3005 12.6181 20.0355 12.1008 19.5582C11.5835 19.0808 11.2649 18.4261 11.2086 17.7244C9.34126 17.0634 7.76747 15.764 6.76503 14.0555C5.76259 12.347 5.39597 10.3393 5.72987 8.38676C6.06378 6.43424 7.07674 4.66246 8.58995 3.38416C10.1032 2.10587 12.0193 1.40328 14.0002 1.40039C16.107 1.40009 18.1371 2.19151 19.6878 3.61775C21.2385 5.04398 22.1966 7.00087 22.3722 9.10039C22.378 9.19146 22.3649 9.28275 22.3336 9.36846C22.3022 9.45417 22.2534 9.53243 22.1902 9.59826C22.127 9.66409 22.0508 9.71605 21.9665 9.75085C21.8821 9.78565 21.7914 9.80252 21.7002 9.80039C21.5127 9.79569 21.3336 9.72144 21.1978 9.59208C21.0619 9.46271 20.979 9.28746 20.9652 9.10039C20.8405 7.86256 20.3882 6.68032 19.6548 5.67539C18.9214 4.67046 17.9334 3.87915 16.7925 3.38291C15.6517 2.88668 14.3992 2.70346 13.164 2.85211C11.9288 3.00077 10.7556 3.47592 9.76507 4.22867C8.77454 4.98141 8.00252 5.98454 7.52849 7.13479C7.05446 8.28504 6.89554 9.54084 7.06811 10.7729C7.24067 12.005 7.73848 13.1688 8.51028 14.1446C9.28207 15.1203 10.3 15.8727 11.4592 16.3244C11.6701 15.8686 11.9998 15.478 12.4135 15.1934C12.8273 14.9088 13.31 14.7407 13.8111 14.7068C14.3122 14.6729 14.8131 14.7743 15.2615 15.0005C15.7099 15.2268 16.0892 15.5694 16.3596 15.9926C16.63 16.4157 16.7817 16.9039 16.7987 17.4058C16.8156 17.9077 16.6973 18.405 16.456 18.8454C16.2148 19.2859 15.8595 19.6534 15.4275 19.9094C14.9954 20.1653 14.5024 20.3004 14.0002 20.3004ZM7.0128 16.8004H7.1402C7.9667 17.6112 8.93033 18.2691 9.9864 18.7436C10.2968 19.7676 10.9863 20.6346 11.9142 21.1675C12.842 21.7003 13.9383 21.859 14.9792 21.611C16.0201 21.363 16.9271 20.7271 17.515 19.8331C18.103 18.9391 18.3275 17.8544 18.1428 16.8004H21.0002C21.7428 16.8004 22.455 17.0954 22.9801 17.6205C23.5052 18.1456 23.8002 18.8578 23.8002 19.6004C23.8002 21.9678 22.634 23.7528 20.8112 24.9162C19.0164 26.06 16.5972 26.6004 14.0002 26.6004C11.4032 26.6004 8.984 26.06 7.1892 24.9162C5.3664 23.7542 4.2002 21.9664 4.2002 19.6004C4.2002 18.0422 5.4644 16.8004 7.0128 16.8004ZM19.6002 9.80039C19.6005 10.7477 19.3605 11.6796 18.9026 12.5089C18.4448 13.3382 17.784 14.0378 16.9822 14.5422C16.5917 14.1486 16.1272 13.8363 15.6154 13.6231C15.1035 13.41 14.5546 13.3003 14.0002 13.3004C13.4458 13.3003 12.8968 13.41 12.385 13.6231C11.8732 13.8363 11.4087 14.1486 11.0182 14.5422C9.95478 13.8734 9.14711 12.8666 8.725 11.6834C8.50965 11.079 8.39979 10.442 8.4002 9.80039C8.4002 8.31518 8.99019 6.8908 10.0404 5.84059C11.0906 4.79039 12.515 4.20039 14.0002 4.20039C15.4854 4.20039 16.9098 4.79039 17.96 5.84059C19.0102 6.8908 19.6002 8.31518 19.6002 9.80039Z"
          fill={fillColor}
        />
      </svg>
    );
  }
  if (type === DashboardIconType.LEAD_GENERATION_TOOL) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 26 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.8337 4.33398C13.217 4.33398 15.167 6.28398 15.167 8.66732C15.167 11.0507 13.217 13.0007 10.8337 13.0007C8.45033 13.0007 6.50033 11.0507 6.50033 8.66732C6.50033 6.28398 8.45033 4.33398 10.8337 4.33398ZM18.417 22.7507L20.367 24.6682C20.9087 25.2098 21.667 24.7765 21.667 24.1373V19.5007L24.7003 15.8173C24.821 15.6564 24.8945 15.465 24.9126 15.2646C24.9307 15.0642 24.8926 14.8628 24.8026 14.6828C24.7126 14.5029 24.5743 14.3516 24.4032 14.2458C24.2321 14.14 24.0348 14.084 23.8337 14.084H16.2503C15.3837 14.084 14.842 15.1673 15.3837 15.8173L18.417 19.5007V22.7507ZM16.2503 20.259L13.7587 17.2257C13.3253 16.684 13.1087 16.034 13.1087 15.384C12.3503 15.1673 11.592 15.1673 10.8337 15.1673C6.06699 15.1673 2.16699 17.1173 2.16699 19.5007V21.6673H16.2503V20.259Z"
          fill={fillColor}
        />
      </svg>
    );
  }
  if (type === DashboardIconType.PRICING) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_233_3209)">
          <path
            d="M22.8751 0H13.8751C13.2571 0 12.3916 0.3585 11.9551 0.795L0.796557 11.9535C0.692014 12.058 0.609083 12.182 0.552501 12.3186C0.49592 12.4551 0.466797 12.6015 0.466797 12.7493C0.466797 12.897 0.49592 13.0434 0.552501 13.1799C0.609083 13.3165 0.692014 13.4405 0.796557 13.545L10.4551 23.2035C10.5595 23.308 10.6836 23.391 10.8201 23.4476C10.9567 23.5041 11.103 23.5333 11.2508 23.5333C11.3986 23.5333 11.545 23.5041 11.6815 23.4476C11.818 23.391 11.9421 23.308 12.0466 23.2035L23.2051 12.045C23.6431 11.607 24.0001 10.743 24.0001 10.125V1.125C23.9989 0.826996 23.88 0.541536 23.6692 0.330814C23.4585 0.120092 23.1731 0.00118492 22.8751 0ZM17.2501 9C16.9545 8.9999 16.6618 8.94159 16.3888 8.82839C16.1158 8.71518 15.8677 8.54931 15.6588 8.34024C15.4498 8.13117 15.2841 7.88299 15.1711 7.60988C15.0581 7.33677 15 7.04407 15.0001 6.7485C15.0002 6.45293 15.0585 6.16027 15.1717 5.88723C15.2849 5.6142 15.4507 5.36613 15.6598 5.1572C15.8689 4.94827 16.1171 4.78256 16.3902 4.66954C16.6633 4.55652 16.956 4.4984 17.2516 4.4985C17.8485 4.4987 18.4209 4.73602 18.8429 5.15826C19.2648 5.5805 19.5018 6.15306 19.5016 6.75C19.5014 7.34694 19.264 7.91934 18.8418 8.3413C18.4196 8.76326 17.847 9.0002 17.2501 9Z"
            fill={fillColor}
          />
        </g>
        <defs>
          <clipPath id="clip0_233_3209">
            <rect width="24" height="24" fill="white" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  if (type === DashboardIconType.LOGOUT) {
    return (
      <svg
        {...sizes}
        viewBox="0 0 26 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.6663 13.0007H11.3747M19.4997 16.2507L22.7497 13.0007L19.4997 9.75065M14.083 7.58398V6.50065C14.083 5.92602 13.8547 5.37492 13.4484 4.96859C13.0421 4.56226 12.491 4.33398 11.9163 4.33398H6.49967C5.92504 4.33398 5.37394 4.56226 4.96761 4.96859C4.56128 5.37492 4.33301 5.92602 4.33301 6.50065V19.5007C4.33301 20.0753 4.56128 20.6264 4.96761 21.0327C5.37394 21.439 5.92504 21.6673 6.49967 21.6673H11.9163C12.491 21.6673 13.0421 21.439 13.4484 21.0327C13.8547 20.6264 14.083 20.0753 14.083 19.5007V18.4173"
          stroke="#005857"
          strokeWidth="2.16667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
  return (
    <svg
      {...sizes}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 12C3 12.2652 3.10536 12.5196 3.29289 12.7071C3.48043 12.8946 3.73478 13 4 13H10C10.2652 13 10.5196 12.8946 10.7071 12.7071C10.8946 12.5196 11 12.2652 11 12V4C11 3.73478 10.8946 3.48043 10.7071 3.29289C10.5196 3.10536 10.2652 3 10 3H4C3.73478 3 3.48043 3.10536 3.29289 3.29289C3.10536 3.48043 3 3.73478 3 4V12ZM3 20C3 20.2652 3.10536 20.5196 3.29289 20.7071C3.48043 20.8946 3.73478 21 4 21H10C10.2652 21 10.5196 20.8946 10.7071 20.7071C10.8946 20.5196 11 20.2652 11 20V16C11 15.7348 10.8946 15.4804 10.7071 15.2929C10.5196 15.1054 10.2652 15 10 15H4C3.73478 15 3.48043 15.1054 3.29289 15.2929C3.10536 15.4804 3 15.7348 3 16V20ZM13 20C13 20.2652 13.1054 20.5196 13.2929 20.7071C13.4804 20.8946 13.7348 21 14 21H20C20.2652 21 20.5196 20.8946 20.7071 20.7071C20.8946 20.5196 21 20.2652 21 20V12C21 11.7348 20.8946 11.4804 20.7071 11.2929C20.5196 11.1054 20.2652 11 20 11H14C13.7348 11 13.4804 11.1054 13.2929 11.2929C13.1054 11.4804 13 11.7348 13 12V20ZM14 3C13.7348 3 13.4804 3.10536 13.2929 3.29289C13.1054 3.48043 13 3.73478 13 4V8C13 8.26522 13.1054 8.51957 13.2929 8.70711C13.4804 8.89464 13.7348 9 14 9H20C20.2652 9 20.5196 8.89464 20.7071 8.70711C20.8946 8.51957 21 8.26522 21 8V4C21 3.73478 20.8946 3.48043 20.7071 3.29289C20.5196 3.10536 20.2652 3 20 3H14Z"
        fill={fillColor}
      />
    </svg>
  );
};

export const LogoutIcon = () => {
  return (
    <svg
      {...sizes}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.6663 13.0007H11.3747M19.4997 16.2507L22.7497 13.0007L19.4997 9.75065M14.083 7.58398V6.50065C14.083 5.92602 13.8547 5.37492 13.4484 4.96859C13.0421 4.56226 12.491 4.33398 11.9163 4.33398H6.49967C5.92504 4.33398 5.37394 4.56226 4.96761 4.96859C4.56128 5.37492 4.33301 5.92602 4.33301 6.50065V19.5007C4.33301 20.0753 4.56128 20.6264 4.96761 21.0327C5.37394 21.439 5.92504 21.6673 6.49967 21.6673H11.9163C12.491 21.6673 13.0421 21.439 13.4484 21.0327C13.8547 20.6264 14.083 20.0753 14.083 19.5007V18.4173"
        stroke="#005857"
        strokeWidth="2.16667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const UserIcon = () => {
  return (
    <svg
      {...sizes}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.9997 4.33203C14.1489 4.33203 15.2511 4.78858 16.0638 5.60124C16.8765 6.41389 17.333 7.51609 17.333 8.66536C17.333 9.81464 16.8765 10.9168 16.0638 11.7295C15.2511 12.5422 14.1489 12.9987 12.9997 12.9987C11.8504 12.9987 10.7482 12.5422 9.93554 11.7295C9.12289 10.9168 8.66634 9.81464 8.66634 8.66536C8.66634 7.51609 9.12289 6.41389 9.93554 5.60124C10.7482 4.78858 11.8504 4.33203 12.9997 4.33203ZM12.9997 6.4987C12.425 6.4987 11.8739 6.72697 11.4676 7.1333C11.0613 7.53963 10.833 8.09073 10.833 8.66536C10.833 9.24 11.0613 9.7911 11.4676 10.1974C11.8739 10.6038 12.425 10.832 12.9997 10.832C13.5743 10.832 14.1254 10.6038 14.5317 10.1974C14.9381 9.7911 15.1663 9.24 15.1663 8.66536C15.1663 8.09073 14.9381 7.53963 14.5317 7.1333C14.1254 6.72697 13.5743 6.4987 12.9997 6.4987ZM12.9997 14.082C15.8922 14.082 21.6663 15.5229 21.6663 18.4154V21.6654H4.33301V18.4154C4.33301 15.5229 10.1072 14.082 12.9997 14.082ZM12.9997 16.1404C9.78217 16.1404 6.39134 17.722 6.39134 18.4154V19.607H19.608V18.4154C19.608 17.722 16.2172 16.1404 12.9997 16.1404Z"
        fill="#005857"
      />
    </svg>
  );
};
export default DashboardIcon;
