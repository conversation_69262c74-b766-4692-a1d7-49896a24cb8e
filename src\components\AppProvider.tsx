/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Maybe } from "@/types/common";
import { UsersType } from "@/types/db";
import { useContext, createContext, useState } from "react";

type UserContextType = {
  userData: Maybe<UsersType>;
  updateUser: (updates: Partial<UsersType>) => void;
};

const UserContext = createContext<UserContextType>({
  userData: null,
  updateUser: () => {},
});

type AppProviderProps = {
  userData: UsersType | null;
  children: React.ReactNode;
};

export function AppProvider({
  userData: initialUserData,
  children,
}: AppProviderProps) {
  const [userData, setUserData] = useState<Maybe<UsersType>>(initialUserData);

  const updateUser = (updates: Partial<UsersType>) => {
    setUserData((prev) => (prev ? { ...prev, ...updates } : null));
  };

  return (
    <UserContext.Provider value={{ userData, updateUser }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  return context.userData;
}

export function useUpdateUser() {
  const context = useContext(UserContext);
  return context.updateUser;
}
