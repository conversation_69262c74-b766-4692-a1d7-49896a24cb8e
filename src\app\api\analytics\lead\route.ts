/* eslint-disable @typescript-eslint/no-unused-vars */
import { createClient } from "@/lib/server";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // const supabase = await createClient();
    // if (error) {
    //   console.error("Failed to fetch leads:", error);
    //   return NextResponse.json(
    //     { error: "Failed to fetch leads" },
    //     { status: 500 }
    //   );
    // }
    // return NextResponse.json(data);
    return NextResponse.json({
      message: "Fetched leads usage successfully",
      success: true,
      data: {
        total_leads_collected: 0,
        total_lead_conversion: 0,
        total_lead_contacted: 0,
      },
    });
  } catch (error) {
    console.error("Failed to fetch leads:", error);
    return NextResponse.json(
      { error: "Failed to fetch leads" },
      { status: 500 }
    );
  }
}
