import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Extracts a display name from an email address by removing the domain
 * and capitalizing the first letter of each word (separated by dots, underscores, or hyphens).
 *
 * @param email - The email address to extract the name from
 * @returns A formatted name string
 *
 * @example
 * getNameFromEmail("<EMAIL>") // "<PERSON>"
 * getNameFromEmail("<EMAIL>") // "<PERSON>"
 * getNameFromEmail("<EMAIL>") // "User123"
 */
export function getNameFromEmail(email: string): string {
  if (!email || !email.includes("@")) {
    return "";
  }

  // Extract the part before the @ symbol
  const localPart = email.split("@")[0];

  // Split by common separators (dots, underscores, hyphens)
  const nameParts = localPart.split(/[._-]+/);

  // Capitalize first letter of each part and join with spaces
  return nameParts
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join(" ");
}
