"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { UsersType } from "@/types/db";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { LogoutIcon, UserIcon } from "@/assets/DashboardIcon";
import { createClient } from "@/lib/client";
import { useRouter } from "next/navigation";

const getInitials = (email: string) => {
  return email.charAt(0).toUpperCase();
};

type UserDropdownProps = React.FC<{ userData: UsersType }>;
const UserDropdown: UserDropdownProps = ({ userData }) => {
  const router = useRouter();

  const logout = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.push("/auth/login");
  };

  return (
    <span>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Avatar className="w-10 h-10">
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback>
              {userData?.email ? getInitials(userData.email) : "U"}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 glassColor" align="start">
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => {
              router.push("/dashboard/profile");
            }}
          >
            <UserIcon />
            <span className={`font-bold text-[#005857]`}>Profile page</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => {
              logout();
            }}
          >
            <LogoutIcon />
            <span className={`font-bold text-[#005857]`}>Logout</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </span>
  );
};

export default UserDropdown;
