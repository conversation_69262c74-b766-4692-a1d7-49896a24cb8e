import { createClient } from "@/lib/server";
import { createCustomerId } from "@/lib/stripe";
import { SupabaseClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const next = searchParams.get("next") || "/dashboard";

    console.log("OAuth callback called with:", {
      next,
      url: request.url,
    });

    const supabase = await createClient();

    // Get the current user from the session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    console.log("OAuth callback - User check:", {
      hasUser: !!user,
      userId: user?.id,
      email: user?.email,
      error: userError?.message,
    });

    if (userError || !user) {
      console.error("OAuth callback - No user found:", userError);
      return NextResponse.redirect(
        new URL("/auth/error?error=Authentication failed", request.url)
      );
    }

    // Check if user already exists in our users table
    const { data: existingUser, error: getUserError } = await supabase
      .from("users")
      .select("id, email")
      .eq("id", user.id)
      .maybeSingle();

    if (getUserError) {
      console.error("Error checking existing user:", getUserError);
      return NextResponse.redirect(
        new URL("/auth/error?error=Database error", request.url)
      );
    }

    // If user doesn't exist in our users table, create them
    if (!existingUser) {
      // Check if there's already a user with this email (from email/password signup)
      const { data: emailUser, error: emailUserError } = await supabase
        .from("users")
        .select("id, email, provider")
        .eq("email", user.email)
        .maybeSingle();

      if (emailUserError) {
        console.error("Error checking email user:", emailUserError);
        return NextResponse.redirect(
          new URL("/auth/error?error=Database error", request.url)
        );
      }

      if (emailUser) {
        // User already exists with this email but different auth method
        // Only show conflict if they used email/password provider
        if (emailUser.provider === "email") {
          // This is a conflict - redirect to a special page to handle account linking
          return NextResponse.redirect(
            new URL(
              `/auth/account-conflict?email=${encodeURIComponent(
                user.email || ""
              )}`,
              request.url
            )
          );
        }
        // If provider is already "google", this shouldn't happen, but just continue
      }

      // Create new user record
      const planId = await getFreePlanId(supabase);

      if (!planId) {
        console.error("Failed to get free plan ID");
        return NextResponse.redirect(
          new URL("/auth/error?error=Failed to assign plan", request.url)
        );
      }

      // Create Stripe customer
      const stripeCustomerId = await createCustomerId(
        user?.email ?? "",
        user.id
      );

      const { error: insertError } = await supabase.from("users").insert({
        id: user.id,
        email: user.email,
        plan_id: planId,
        provider: "google",
        stripecustomerid: stripeCustomerId,
        createdAt: new Date().toISOString(),
      });

      if (insertError) {
        console.error("Failed to create user profile:", insertError);
        return NextResponse.redirect(
          new URL(
            "/auth/error?error=Failed to create user profile",
            request.url
          )
        );
      }

      console.log("Successfully created OAuth user profile for:", user.email);
    }

    // Redirect to the intended destination
    return NextResponse.redirect(new URL(next, request.url));
  } catch (error) {
    console.error("OAuth callback error:", error);
    return NextResponse.redirect(
      new URL("/auth/error?error=An unexpected error occurred", request.url)
    );
  }
}

async function getFreePlanId(supabase: SupabaseClient) {
  const { data } = await supabase
    .from("plans")
    .select("id")
    .eq("name", "Free")
    .single();
  return data?.id;
}
